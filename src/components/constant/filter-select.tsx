"use client";

import React from "react";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";

interface Option {
  value: string;
  label: string;
  color?: string; // optional (for colored dots like Active/Inactive)
}

interface FilterSelectProps {
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
  options: Option[];
  className?: string;
}

const FilterSelect: React.FC<FilterSelectProps> = ({
  placeholder,
  value,
  onChange,
  options,
  className,
}) => {
  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger
        className={`w-[160px] bg-bg-card border border-[#fff]/40 text-text-primary 
                    hover:bg-bg-card-hover hover:border-primary/30 
                    focus:border-primary focus:ring-2 focus:ring-primary/20 
                    transition-all duration-200 rounded-lg shadow-sm ${className}`}
      >
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent className="bg-popover border-border-subtle rounded-lg shadow-lg animate-fadeIn">
        {options.map((opt) => (
          <SelectItem
            key={opt.value}
            value={opt.value}
            className="text-text-primary hover:bg-accent"
          >
            <div className="flex items-center gap-2">
              {opt.color && (
                <span
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: opt.color }}
                ></span>
              )}
              {opt.label}
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default FilterSelect;
