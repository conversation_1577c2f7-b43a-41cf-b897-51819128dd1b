"use client"

import type React from "react"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { X, Eye, EyeOff } from "lucide-react"

interface AddTeamMemberModalProps {
  isOpen: boolean
  onClose: () => void
}

export function AddTeamMemberModal({ isOpen, onClose }: AddTeamMemberModalProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    password: "",
    role: "",
  })

  if (!isOpen) return null

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log("Team member data:", formData)
    onClose()
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />

      {/* Modal */}
      <div className="relative w-full max-w-md bg-black/20 backdrop-blur-2xl border border-white/10 rounded-2xl p-8 shadow-2xl ring-1 ring-white/5">
        {/* Close button */}
        <button onClick={onClose} className="absolute top-6 right-6 text-white/70 hover:text-white transition-colors">
          <X size={24} />
        </button>

        {/* Header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-2">Add New Team Member</h2>
          <p className="text-white/70">Create a new team member account with role assignment</p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Full Name */}
          <div className="space-y-2">
            <Label htmlFor="fullName" className="text-white font-medium">
              Full Name
            </Label>
            <Input
              id="fullName"
              type="text"
              placeholder="Enter Full Name"
              value={formData.fullName}
              onChange={(e) => handleInputChange("fullName", e.target.value)}
              className="bg-white/5 backdrop-blur-sm border-white/10 text-white placeholder:text-white/40 focus:border-white/20 focus:ring-white/10 focus:bg-white/10"
            />
          </div>

          {/* Email */}
          <div className="space-y-2">
            <Label htmlFor="email" className="text-white font-medium">
              Email Address
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter Email Address"
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              className="bg-white/5 backdrop-blur-sm border-white/10 text-white placeholder:text-white/40 focus:border-white/20 focus:ring-white/10 focus:bg-white/10"
            />
          </div>

          {/* Password */}
          <div className="space-y-2">
            <Label htmlFor="password" className="text-white font-medium">
              Password
            </Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="Enter Password"
                value={formData.password}
                onChange={(e) => handleInputChange("password", e.target.value)}
                className="bg-white/5 backdrop-blur-sm border-white/10 text-white placeholder:text-white/40 focus:border-white/20 focus:ring-white/10 focus:bg-white/10 pr-12"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-white/70 hover:text-white transition-colors"
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
          </div>

          {/* Role */}
          <div className="space-y-2">
            <Label htmlFor="role" className="text-white font-medium">
              Role
            </Label>
            <Select value={formData.role} onValueChange={(value) => handleInputChange("role", value)}>
              <SelectTrigger className="bg-white/5 backdrop-blur-sm border-white/10 text-white focus:border-white/20 focus:ring-white/10 focus:bg-white/10">
                <SelectValue placeholder="Select a Role" className="text-white/40" />
              </SelectTrigger>
              <SelectContent className="bg-black/80 backdrop-blur-2xl border-white/10 ring-1 ring-white/5">
                <SelectItem value="admin" className="text-white hover:bg-white/10 focus:bg-white/10">
                  Admin
                </SelectItem>
                <SelectItem value="manager" className="text-white hover:bg-white/10 focus:bg-white/10">
                  Manager
                </SelectItem>
                <SelectItem value="developer" className="text-white hover:bg-white/10 focus:bg-white/10">
                  Developer
                </SelectItem>
                <SelectItem value="designer" className="text-white hover:bg-white/10 focus:bg-white/10">
                  Designer
                </SelectItem>
                <SelectItem value="viewer" className="text-white hover:bg-white/10 focus:bg-white/10">
                  Viewer
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1 bg-white/5 backdrop-blur-sm border-white/10 text-white hover:bg-white/10 hover:border-white/20"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-green-600/90 backdrop-blur-sm hover:bg-green-600 text-white border-0 ring-1 ring-green-500/20"
            >
              Add Team Member
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
