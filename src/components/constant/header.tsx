"use client";

import { FC } from "react";

interface HeaderProps {
  title?: string;
}

const Header: FC<HeaderProps> = ({ title }) => {
  return (
    <header className="fixed top-0 left-0 right-0 h-14 bg-zinc-900 border-b border-zinc-800 flex items-center justify-between px-6 z-50">
      {/* Left: Logo + Title */}
      <div className="flex items-center gap-2">
        <div className="h-8 w-8 bg-primary rounded-md flex items-center justify-center font-bold">
          L
        </div>
        <span className="font-semibold">{title ?? "MyApp"}</span>
      </div>

      {/* Right: Profile */}
      <div className="flex items-center gap-4">
        <span className="text-sm"><PERSON></span>
        <img
          src="https://via.placeholder.com/32"
          alt="profile"
          className="h-8 w-8 rounded-full border border-gray-600"
        />
      </div>
    </header>
  );
};

export default Header;
