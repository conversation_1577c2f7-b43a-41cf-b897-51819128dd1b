"use client";

import MainLayout from "@/layouts/main-layout";
import React, { useState } from "react";
import { dummyMembers } from "@/lib/dummy-data";
import { DataCard } from "@/components/constant/data-card";
import { Button } from "@/components/ui/button";
import { Eye, Edit, Trash2, Search, Filter } from "lucide-react";
import { Input } from "@/components/ui/input";
import { ActionButton, Badge } from "@/models/common";
import FilterSelect from "@/components/constant/filter-select";
import { AddTeamMemberModal } from "@/components/constant/add-team-member-modal";

const badges: Badge[] = [
  {
    label: "Active",
    dotColor: "#22c55e", // green-500
    className: "bg-[#FFFFFF]/4 border border-[#fff]/40 text-white",
  },
  {
    label: "Editor",
    className: "bg-[#FFFFFF]/4 border border-[#fff]/40 text-white",
    dotColor: "#3b82f6", // blue-500
  },
];

const buttons: ActionButton[] = [
  {
    label: "View",
    icon: Eye,
    className: "bg-[#D7E1E4] hover:bg-[#D7E1E4]/90 text-black",
  },
  {
    label: "Edit",
    icon: Edit,
    className: "bg-[#D7E1E4] hover:bg-[#D7E1E4]/90 text-black",
  },
  {
    label: "Deactivate",
    icon: Trash2,
    className: "bg-[#D1E4EB]/64 hover:bg-[#D1E4EB]/30 text-[#CD0035]",
  },
];

const statusOptions = [
  { value: "all", label: "All Status" },
  { value: "active", label: "Active", color: "#22c55e" },
  { value: "inactive", label: "Inactive", color: "#ef4444" },
  { value: "pending", label: "Pending", color: "#eab308" },
];

const planOption = [
  { value: "all", label: "All plans" },
  { value: "admin", label: "Admin", color: "#a855f7" },
  { value: "editor", label: "Editor", color: "#3b82f6" },
  { value: "viewer", label: "Viewer", color: "#6b7280" },
];

export default function SubscribersPage() {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [planFilter, setPlanFilter] = useState<string>("all");
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <MainLayout>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        {/* Left Section */}
        <div>
          <h1 className="text-2xl font-bold text-text-primary">
            Team Management
          </h1>
          <p className="text-text-secondary mt-1">
            Manage internal team members and their roles
          </p>
        </div>

        {/* Right Section */}
        <div>
          <Button
            className="bg-[#016E01] text-white hover:bg-[#016E01]/90 rounded-lg shadow-md"
            onClick={() => setIsModalOpen(true)}
          >
            + Add Team Member
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 animate-fadeIn">
        {/* Left: Search */}
        <div className="flex-1 relative group">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-text-muted group-focus-within:text-primary transition-colors" />
          </div>
          <Input
            type="text"
            placeholder="Search team members by name or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 bg-bg-card border border-[#fff]/40 text-text-primary placeholder:text-text-muted 
             hover:bg-bg-card-hover hover:border-primary/40 
             focus:border-primary focus:ring-2 focus:ring-primary/20 
             transition-all duration-200 rounded-lg shadow-sm"
          />
        </div>
        <div className="flex gap-3 items-center">
          <div className="flex items-center gap-2 text-text-secondary text-sm">
            <Filter className="h-4 w-4" />
            <span>Filters:</span>
          </div>

          {/* Status Filter */}
          <FilterSelect
            placeholder="Status"
            value={statusFilter}
            onChange={setStatusFilter}
            options={statusOptions}
          />

          <FilterSelect
            placeholder="plans"
            value={planFilter}
            onChange={setPlanFilter}
            options={planOption}
          />
        </div>
      </div>

      {/* Data Cards */}
      <div className="space-y-4 full-width-container">
        {dummyMembers.map((member) => {
          // Generate JSX buttons dynamically
          const actions = buttons.map((btn, idx) => {
            const Icon = btn.icon;
            return (
              <Button key={idx} size="sm" className={btn.className}>
                <Icon className="h-4 w-4 mr-1" /> {btn.label}
              </Button>
            );
          });

          return (
            <DataCard
              key={member.id}
              id={member.id}
              avatar={`https://images.ctfassets.net/h6goo9gw1hh6/2sNZtFAWOdP1lmQ33VwRN3/24e953b920a9cd0ff2e1d587742a2472/1-intro-photo-final.jpg?w=1200&h=992&fl=progressive&q=70&fm=jpg`}
              fallback={member.name
                .split(" ")
                .map((n) => n[0])
                .join("")}
              title={member.name}
              subtitle={member.email}
              badges={badges}
              actions={actions}
            />
          );
        })}
      </div>
      <AddTeamMemberModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </MainLayout>
  );
}
