"use client";
import { Input } from "@/components/ui/input";
import AuthLayout from "@/layouts/auth-layout";
import { useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import { Auth } from "@/models/auth";
import { useRouter } from "next/navigation";

export default function Home() {
  const [formData, setFormData] = useState<Auth>({
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const router = useRouter();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const togglePassword = () => {
    setShowPassword((prev) => !prev);
  };
  console.log(formData);
  return (
    <AuthLayout>
      <div className="flex flex-col gap-4">
        <h1 className="text-3xl text-center font-bold">Admin <PERSON></h1>
        <div className="flex text-lg text-center justify-center gap-4">
          Please enter your credentials to access the admin panel.
        </div>
        <div className="flex flex-col gap-4">
          <Input
            type="text"
            name="email"
            value={formData.email}
            onChange={handleChange}
            placeholder="Email"
          />
          <div className="relative">
            <Input
              type={showPassword ? "text" : "password"}
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Password"
              className="pr-10" // 👈 space for the icon
            />
            <button
              type="button"
              onClick={togglePassword}
              className="absolute inset-y-0 right-3 flex items-center text-gray-500 hover:text-gray-700"
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5" />
              ) : (
                <Eye className="h-5 w-5" />
              )}
            </button>
          </div>
          <button
            className="bg-primary text-white rounded-lg p-2 hover:bg-primary/80"
            onClick={() => router.push("/dashboard")}
          >
            Login
          </button>
        </div>
      </div>
    </AuthLayout>
  );
}
